/* eslint-disable react-hooks/exhaustive-deps */
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Button,
  Card,
  EmptyState,
  FormItem,
  Icon,
  Input,
  Modal,
  Typography,
} from '@/shared/components/common';
import IconCard from '@/shared/components/common/IconCard';

import { ModernMenuItem } from '@/shared/components/common/ModernMenu';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { NotificationUtil } from '@/shared/utils/notification';
import {
  useCreateWebsite,
  useDeleteWebsite,
  useGetWidgetScript,
  useUpdateWebsiteLogo,
} from '../website/hooks/useWebsite';
import { CreateWebsiteFormData, createWebsiteSchema } from '../schemas/website.schema';
import {
  SortDirection,
  WebsiteDto,
  WebsiteQueryDto,
  WebsiteSortBy,
  CreateWebsiteDto,
} from '../website/types/website.types';
import { crawlWebsiteLogo, uploadLogoToPresignedUrl } from '../utils/logo-crawler';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

interface WebsiteManagementComponentProps {
  /**
   * Danh sách websites hiện tại để hiển thị
   */
  result: PaginatedResult<WebsiteDto> | undefined;

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;

  /**
   * Callback khi cần refresh danh sách
   */
  onRefresh?: () => void;

  /**
   * Callback khi filter thay đổi
   */
  onFilterChange?: (filters: WebsiteQueryDto) => void;

  /**
   * Current filter values
   */
  currentFilters?: WebsiteQueryDto;
}

/**
 * Component quản lý Website - thay thế cho SlideInForm
 */
const WebsiteManagementComponent: React.FC<WebsiteManagementComponentProps> = ({
  result,
  isLoading = false,
  onRefresh,
  onFilterChange,
  currentFilters = {},
}) => {
  const { t } = useTranslation('integration');

  // State để hiển thị form thêm mới
  const [showAddForm, setShowAddForm] = useState(false);

  // State cho modal xóa
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [websiteToDelete, setWebsiteToDelete] = useState<string | null>(null);

  // State cho lỗi API trong form
  const [formApiError, setFormApiError] = useState<string | null>(null);

  // State cho copy script
  const [isCopied, setIsCopied] = useState(false);

  // State cho logo upload
  const [selectedLogo, setSelectedLogo] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  // API hooks
  const createWebsiteMutation = useCreateWebsite();
  const deleteWebsiteMutation = useDeleteWebsite();
  const updateWebsiteLogoMutation = useUpdateWebsiteLogo();
  const { data: widgetScriptData } = useGetWidgetScript();

  // Form setup với react-hook-form và zod validation
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isValid, isDirty },
  } = useForm<CreateWebsiteFormData>({
    resolver: zodResolver(createWebsiteSchema),
    mode: 'onChange',
    defaultValues: {
      websiteName: '',
      host: '',
    },
  });

  // Xử lý submit form với logo crawling trong background
  const onSubmit = useCallback(
    async (data: CreateWebsiteFormData) => {
      // Clear previous error
      setFormApiError(null);

      try {
        // 1. Chuẩn bị dữ liệu tạo website
        let logoMime: string | null = null;

        // Nếu có logo được chọn, sử dụng logo đó
        if (selectedLogo) {
          logoMime = selectedLogo.type;
        } else {
          // Nếu không có logo được chọn, thử crawl logo từ website
          try {
            const logoInfo = await crawlWebsiteLogo(data.host);
            if (logoInfo) {
              logoMime = logoInfo.mimeType;
            }
          } catch (logoError) {
            console.error('Logo crawling failed:', logoError);
          }
        }

        // 2. Tạo website với logoMime
        const createData: CreateWebsiteDto = {
          ...data,
          ...(logoMime && { logoMime }),
        };

        const response = await createWebsiteMutation.mutateAsync(createData);

        // 3. Upload logo nếu có
        if (response.result.logoUploadUrl) {
          try {
            if (selectedLogo) {
              // Upload logo được chọn
              await uploadLogoToPresignedUrl(selectedLogo, response.result.logoUploadUrl);
              console.log('Selected logo uploaded successfully');
            } else {
              // Fallback: thử crawl và upload logo
              const logoInfo = await crawlWebsiteLogo(data.host);
              if (logoInfo) {
                await uploadLogoToPresignedUrl(logoInfo.blob, response.result.logoUploadUrl);
                console.log('Crawled logo uploaded successfully');
              }
            }
          } catch (uploadError) {
            console.error('Logo upload error:', uploadError);
            // Không fail toàn bộ process nếu upload logo thất bại
          }
        }

        // Hiển thị thông báo thành công
        NotificationUtil.success({
          title: t('website.createSuccess', 'Tạo website thành công!'),
          message: t('website.createSuccessDesc', 'Website đã được thêm vào danh sách.'),
          duration: 3000,
        });

        // Reset form và clear error
        reset();
        setFormApiError(null);
        setSelectedLogo(null);
        setLogoPreview(null);
        setShowAddForm(false);

        // Refresh danh sách
        if (onRefresh) {
          onRefresh();
        }
      } catch (error: unknown) {
        console.error('Error creating website:', error);

        // Xử lý lỗi API và hiển thị trong form
        let errorMessage = t('website.createErrorDesc', 'Vui lòng thử lại sau.');

        // Kiểm tra lỗi từ API response
        if (error && typeof error === 'object' && 'response' in error) {
          const apiError = error as { response?: { data?: { message?: string } } };
          if (apiError.response?.data?.message) {
            errorMessage = apiError.response.data.message;
          }
        } else if (error && typeof error === 'object' && 'message' in error) {
          const generalError = error as { message: string };
          errorMessage = generalError.message;
        }

        // Set lỗi vào state để hiển thị trong form
        setFormApiError(errorMessage);
      }
    },
    [createWebsiteMutation, reset, onRefresh, t]
  );

  // Xử lý hủy form
  const handleCancel = useCallback(() => {
    if (isDirty && !createWebsiteMutation.isPending) {
      const confirmed = window.confirm(
        t('common.unsavedChanges', 'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn hủy?')
      );
      if (!confirmed) return;
    }

    reset();
    setFormApiError(null); // Clear API error khi hủy
    setSelectedLogo(null);
    setLogoPreview(null);
    setShowAddForm(false);
  }, [isDirty, createWebsiteMutation.isPending, reset, t]);

  // Xử lý hiển thị form thêm mới
  const handleShowAddForm = () => {
    setFormApiError(null); // Clear previous error khi mở form
    setShowAddForm(true);
  };

  // Xử lý xóa website
  const handleDeleteWebsite = useCallback((websiteId: string) => {
    setWebsiteToDelete(websiteId);
    setShowDeleteModal(true);
  }, []);

  // Xác nhận xóa website
  const handleConfirmDelete = useCallback(async () => {
    if (!websiteToDelete) return;

    try {
      await deleteWebsiteMutation.mutateAsync(websiteToDelete);

      NotificationUtil.success({
        title: t('website.deleteSuccess', 'Xóa website thành công!'),
        message: t('website.deleteSuccessDesc', 'Website đã được xóa khỏi danh sách.'),
        duration: 3000,
      });

      // Refresh danh sách
      if (onRefresh) {
        onRefresh();
      }

      // Đóng modal
      setShowDeleteModal(false);
      setWebsiteToDelete(null);
    } catch (error) {
      console.error('Error deleting website:', error);

      NotificationUtil.error({
        title: t('website.deleteError', 'Xóa website thất bại!'),
        message: t('website.deleteErrorDesc', 'Vui lòng thử lại sau.'),
        duration: 5000,
      });
    }
  }, [websiteToDelete, deleteWebsiteMutation, onRefresh, t]);

  // Hủy xóa website
  const handleCancelDelete = useCallback(() => {
    setShowDeleteModal(false);
    setWebsiteToDelete(null);
  }, []);

  // Handle filter change
  const handleFilterChange = useCallback(
    (newFilters: Partial<WebsiteQueryDto>) => {
      if (onFilterChange) {
        onFilterChange({ ...currentFilters, ...newFilters });
      }
    },
    [currentFilters, onFilterChange]
  );

  // Handle search
  const handleSearch = useCallback(
    (searchTerm: string) => {
      handleFilterChange({ search: searchTerm });
    },
    [handleFilterChange]
  );

  // Filter menu items
  const filterMenuItems: ModernMenuItem[] = useMemo(
    () => [
      {
        id: 'sortBy-websiteName',
        label: t('website.filter.websiteName', 'Tên Website'),
        onClick: () => handleFilterChange({ sortBy: WebsiteSortBy.WEBSITE_NAME }),
      },
      {
        id: 'sortBy-host',
        label: t('website.filter.host', 'Host'),
        onClick: () => handleFilterChange({ sortBy: WebsiteSortBy.HOST }),
      },
      {
        id: 'sortBy-createdAt',
        label: t('website.filter.createdAt', 'Ngày tạo'),
        onClick: () => handleFilterChange({ sortBy: WebsiteSortBy.CREATED_AT }),
      },
      {
        id: 'sortBy-verify',
        label: t('website.filter.verify', 'Trạng thái xác thực'),
        onClick: () => handleFilterChange({ sortBy: WebsiteSortBy.VERIFY }),
      },
      {
        id: 'divider-1',
        divider: true,
      },
      {
        id: 'sortDirection-asc',
        label: t('website.filter.asc', 'Tăng dần'),
        icon: 'arrow-up',
        onClick: () => handleFilterChange({ sortDirection: SortDirection.ASC }),
      },
      {
        id: 'sortDirection-desc',
        label: t('website.filter.desc', 'Giảm dần'),
        icon: 'arrow-down',
        onClick: () => handleFilterChange({ sortDirection: SortDirection.DESC }),
      },
      {
        id: 'divider-2',
        divider: true,
      },
      {
        id: 'verify-all',
        label: t('website.filter.all', 'Tất cả'),
        onClick: () => {
          const filters: Partial<WebsiteQueryDto> = {};
          handleFilterChange(filters);
        },
      },
      {
        id: 'verify-verified',
        label: t('website.filter.verified', 'Đã xác thực'),
        icon: 'check-circle',
        onClick: () => handleFilterChange({ verify: true }),
      },
      {
        id: 'verify-unverified',
        label: t('website.filter.unverified', 'Chưa xác thực'),
        icon: 'x-circle',
        onClick: () => handleFilterChange({ verify: false }),
      },
    ],
    [handleFilterChange, t]
  );

  // Copy widget script
  const handleCopyScript = useCallback(() => {
    if (widgetScriptData?.result?.script) {
      navigator.clipboard.writeText(widgetScriptData.result.script);

      // Set copied state
      setIsCopied(true);

      // Show success notification
      NotificationUtil.success({
        title: t('website.copySuccess', 'Đã sao chép!'),
        message: t('website.copySuccessDesc', 'Script đã được sao chép vào clipboard.'),
        duration: 2000,
      });
    }
  }, [widgetScriptData, t]);

  // Reset copied state after 3 seconds
  useEffect(() => {
    if (isCopied) {
      const timer = setTimeout(() => {
        setIsCopied(false);
      }, 3000);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [isCopied]);

  // Kiểm tra có filter/search không
  const hasActiveFilters = useMemo(() => {
    return !!(
      currentFilters.search ||
      currentFilters.sortBy ||
      currentFilters.sortDirection ||
      currentFilters.verify !== undefined
    );
  }, [currentFilters]);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    if (onFilterChange) {
      const clearFilters: WebsiteQueryDto = {
        page: 1,
        limit: currentFilters.limit || 10,
      };
      onFilterChange(clearFilters);
    }
  }, [onFilterChange, currentFilters.limit]);

  // Handle logo file selection
  const handleLogoSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          NotificationUtil.error({
            title: t('website.logoError', 'Lỗi logo'),
            message: t('website.logoErrorDesc', 'Chỉ chấp nhận file ảnh'),
            duration: 3000,
          });
          return;
        }

        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
          NotificationUtil.error({
            title: t('website.logoError', 'Lỗi logo'),
            message: t('website.logoSizeError', 'Kích thước file không được vượt quá 5MB'),
            duration: 3000,
          });
          return;
        }

        setSelectedLogo(file);

        // Create preview
        const reader = new FileReader();
        reader.onload = e => {
          setLogoPreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      }
    },
    [t]
  );

  // Handle logo remove
  const handleLogoRemove = useCallback(() => {
    setSelectedLogo(null);
    setLogoPreview(null);
  }, []);

  // Handle logo change for existing website
  const handleLogoChange = useCallback(
    async (websiteId: string, file: File) => {
      try {
        // Validate file
        if (!file.type.startsWith('image/')) {
          NotificationUtil.error({
            title: t('website.logoError', 'Lỗi logo'),
            message: t('website.logoErrorDesc', 'Chỉ chấp nhận file ảnh'),
            duration: 3000,
          });
          return;
        }

        if (file.size > 5 * 1024 * 1024) {
          NotificationUtil.error({
            title: t('website.logoError', 'Lỗi logo'),
            message: t('website.logoSizeError', 'Kích thước file không được vượt quá 5MB'),
            duration: 3000,
          });
          return;
        }

        // const logoMime = file.type;

        const timestamp = Date.now();
        const extension = file.name.split('.').pop();
        const logoS3Key = `integration/website/${websiteId}/logo-${timestamp}.${extension}`;

        // Step 2: Call update logo API
        await updateWebsiteLogoMutation.mutateAsync({
          websiteId,
          logoS3Key,
        });

        NotificationUtil.success({
          title: t('website.logoUpdateSuccess', 'Cập nhật logo thành công!'),
          message: t('website.logoUpdateSuccessDesc', 'Logo website đã được cập nhật.'),
          duration: 3000,
        });

        // Refresh danh sách
        if (onRefresh) {
          onRefresh();
        }
      } catch (error) {
        console.error('Error updating logo:', error);
        NotificationUtil.error({
          title: t('website.logoUpdateError', 'Cập nhật logo thất bại!'),
          message: t('website.logoUpdateErrorDesc', 'Vui lòng thử lại sau.'),
          duration: 5000,
        });
      }
    },
    [updateWebsiteLogoMutation, onRefresh, t]
  );

  return (
    <div className="space-y-6">
      {/* Widget Script Section - Luôn hiển thị khi có websites */}
      <Card>
        <div className="p-4 sm:p-6">
          <div className="mb-4">
            <Typography variant="h6" className="font-semibold text-blue-900 dark:text-blue-100">
              {t('website.widgetScript', 'Widget Script')}
            </Typography>
            <Typography variant="body2" color="muted" className="mt-1">
              {t(
                'website.widgetScriptDesc',
                'Sao chép và dán đoạn script này vào website của bạn để tích hợp chat widget.'
              )}
            </Typography>
          </div>

          <div className="space-y-3">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 border relative group">
              <code className="text-sm font-mono break-all text-gray-800 dark:text-gray-200 pr-10">
                {widgetScriptData?.result?.script || t('common.loading', 'Đang tải...')}
              </code>

              {/* Copy Icon - positioned in top right */}
              {widgetScriptData?.result?.script && (
                <button
                  onClick={handleCopyScript}
                  className="absolute top-3 right-3 p-2 rounded-md bg-white dark:bg-gray-700 shadow-sm border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 opacity-0 group-hover:opacity-100"
                  title={t('website.copyScript', 'Sao chép')}
                >
                  <Icon
                    name={isCopied ? 'check' : 'copy'}
                    size="sm"
                    className={
                      isCopied
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-gray-600 dark:text-gray-300'
                    }
                  />
                </button>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Form thêm website mới */}
      {showAddForm && (
        <Card className="bg-primary/5">
          <div className="p-4 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <Typography variant="h6" className="font-semibold">
                {t('website.createTitle', 'Thêm Website Mới')}
              </Typography>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Website Name Field */}
                <FormItem label={t('website.form.websiteName', 'Tên Website')} required>
                  <Controller
                    name="websiteName"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder={t('website.form.websiteNamePlaceholder', 'Nhập tên website')}
                        disabled={createWebsiteMutation.isPending}
                        fullWidth
                        leftIcon={<Icon name="globe" size="sm" />}
                        error={errors.websiteName?.message}
                      />
                    )}
                  />
                </FormItem>

                {/* Host Field */}
                <FormItem label={t('website.form.host', 'Host/Domain')} required>
                  <div className="space-y-1">
                    <Controller
                      name="host"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder={t(
                            'website.form.hostPlaceholder',
                            'redai.vn hoặc https://www.redai.vn'
                          )}
                          disabled={createWebsiteMutation.isPending}
                          fullWidth
                          leftIcon={<Icon name="link" size="sm" />}
                          error={errors.host?.message}
                        />
                      )}
                    />
                    <Typography variant="caption" color="muted">
                      {t(
                        'website.form.hostDescription',
                        'Nhập domain hoặc URL đầy đủ. Hệ thống sẽ tự động chuẩn hóa.'
                      )}
                    </Typography>
                  </div>
                </FormItem>
              </div>

              {/* Logo Upload Field */}
              <FormItem label={t('website.form.logo', 'Logo Website')}>
                <div className="space-y-3">
                  <div className="flex items-center gap-4">
                    {/* Logo Preview */}
                    {logoPreview ? (
                      <div className="relative">
                        <img
                          src={logoPreview}
                          alt="Logo preview"
                          className="w-16 h-16 object-contain border border-gray-200 dark:border-gray-700 rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={handleLogoRemove}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                        >
                          <Icon name="x" size="xs" />
                        </button>
                      </div>
                    ) : (
                      <div className="w-16 h-16 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center">
                        <Icon name="image" size="md" className="text-gray-400" />
                      </div>
                    )}

                    {/* Upload Button */}
                    <div className="flex-1">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleLogoSelect}
                        className="hidden"
                        disabled={createWebsiteMutation.isPending}
                        id="logo-upload-input"
                      />
                      <label
                        htmlFor="logo-upload-input"
                        className={`cursor-pointer block ${createWebsiteMutation.isPending ? 'pointer-events-none opacity-50' : ''}`}
                      >
                        <div className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors flex items-center justify-center gap-2">
                          <Icon name="upload" size="sm" />
                          {logoPreview
                            ? t('website.form.changeLogo', 'Đổi logo')
                            : t('website.form.uploadLogo', 'Tải lên logo')}
                        </div>
                      </label>
                      <Typography variant="caption" color="muted" className="mt-1 block">
                        {t(
                          'website.form.logoDescription',
                          'Chấp nhận file ảnh (JPEG, PNG, WebP), tối đa 5MB'
                        )}
                      </Typography>
                    </div>
                  </div>
                </div>
              </FormItem>

              {/* API Error Display */}
              {formApiError && (
                <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <div className="flex items-start gap-3">
                    <Icon
                      name="alert-circle"
                      size="sm"
                      className="text-red-500 mt-0.5 flex-shrink-0"
                    />
                    <div className="flex-1">
                      <Typography
                        variant="body2"
                        className="text-red-700 dark:text-red-300 font-medium"
                      >
                        {t('website.createError', 'Tạo website thất bại!')}
                      </Typography>
                      <Typography variant="body2" className="text-red-600 dark:text-red-400 mt-1">
                        {formApiError}
                      </Typography>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={createWebsiteMutation.isPending}
                >
                  {t('common.cancel', 'Hủy')}
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={createWebsiteMutation.isPending}
                  disabled={!isValid || createWebsiteMutation.isPending}
                  className="min-w-[120px]"
                >
                  {createWebsiteMutation.isPending
                    ? t('website.creating', 'Đang tạo...')
                    : t('website.create', 'Tạo')}
                </Button>
              </div>
            </form>
          </div>
        </Card>
      )}
      {/* Danh sách websites */}
      {isLoading ? (
        <Card>
          <div className="flex justify-center items-center py-12">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        </Card>
      ) : !result?.items || result.items.length === 0 ? (
        // Phân biệt giữa không có website và không có kết quả tìm kiếm
        hasActiveFilters ? (
          // Trường hợp tìm kiếm/filter không có kết quả
          <EmptyState
            icon="search"
            title={t('website.noSearchResults', 'Không tìm thấy kết quả')}
            description={t(
              'website.noSearchResultsDescription',
              'Không có website nào phù hợp với tiêu chí tìm kiếm của bạn.'
            )}
            actions={
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  variant="outline"
                  onClick={handleClearFilters}
                  className="flex items-center gap-2"
                >
                  <Icon name="x" size="sm" />
                  {t('website.clearFilters', 'Xóa bộ lọc')}
                </Button>
              </div>
            }
          />
        ) : (
          // Trường hợp chưa có website nào
          <EmptyState
            icon="globe"
            title={t('website.noWebsites', 'Chưa có Website nào')}
            description={t(
              'website.noWebsitesDescription',
              'Bạn chưa thêm Website nào. Hãy thêm Website để bắt đầu.'
            )}
            actions={
              !showAddForm ? (
                <Button
                  variant="primary"
                  onClick={handleShowAddForm}
                  className="flex items-center gap-2"
                >
                  <Icon name="plus" size="sm" />
                  {t('website.addWebsite', 'Thêm Website')}
                </Button>
              ) : undefined
            }
          />
        )
      ) : (
        <div className="space-y-4">
          {/* MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={handleShowAddForm}
            items={filterMenuItems}
            showDateFilter={false}
            showColumnFilter={false}
            isLoading={isLoading}
          />

          {/* Grid websites với loading state riêng */}
          <div className="relative">
            {isLoading ? (
              // Loading skeleton cho danh sách
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Array.from({ length: 6 }).map((_, index) => (
                  <Card key={index} className="animate-pulse">
                    <div className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3 flex-1">
                          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                          <div className="flex-1 min-w-0">
                            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                          <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        </div>
                      </div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : result.items.length === 0 ? (
              // Trường hợp có dữ liệu nhưng items rỗng (filter/search không có kết quả)
              <EmptyState
                icon="search"
                title={t('website.noSearchResults', 'Không tìm thấy kết quả')}
                description={t(
                  'website.noSearchResultsDescription',
                  'Không có website nào phù hợp với tiêu chí tìm kiếm của bạn.'
                )}
                actions={
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button
                      variant="outline"
                      onClick={handleClearFilters}
                      className="flex items-center gap-2"
                    >
                      <Icon name="x" size="sm" />
                      {t('website.clearFilters', 'Xóa bộ lọc')}
                    </Button>
                    {!showAddForm && (
                      <Button
                        variant="primary"
                        onClick={handleShowAddForm}
                        className="flex items-center gap-2"
                      >
                        <Icon name="plus" size="sm" />
                        {t('website.addWebsite', 'Thêm Website')}
                      </Button>
                    )}
                  </div>
                }
              />
            ) : (
              // Danh sách websites thực tế
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-5 gap-6">
                {result.items.map(website => (
                  <Card
                    key={website.id}
                    className="w-full hover:shadow-lg transition-all duration-300"
                  >
                    <div className="p-4 sm:p-6">
                      {/* Header với logo và tên website */}
                      <div className="flex items-center gap-3 mb-4">
                        {/* Logo */}
                        {website.logo ? (
                          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center overflow-hidden flex-shrink-0">
                            <img
                              src={website.logo}
                              alt={`${website.host} logo`}
                              className="w-full h-full object-contain"
                              onError={e => {
                                // Fallback to IconCard nếu logo load fail
                                const target = e.target as HTMLImageElement;
                                const container = target.parentElement;
                                if (container) {
                                  container.innerHTML = '';
                                  const iconCard = document.createElement('div');
                                  iconCard.className =
                                    'w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center bg-gray-200 dark:bg-gray-700';
                                  iconCard.innerHTML =
                                    '<svg class="w-5 h-5 sm:w-6 sm:h-6 text-gray-600 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/></svg>';
                                  container.appendChild(iconCard);
                                }
                              }}
                            />
                          </div>
                        ) : (
                          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center overflow-hidden flex-shrink-0 bg-gray-200 dark:bg-gray-700">
                            <Icon
                              name="website"
                              size="md"
                              className="text-gray-600 dark:text-gray-300"
                            />
                          </div>
                        )}

                        {/* Website info */}
                        <div className="flex-1 min-w-0">
                          <Typography
                            variant="h6"
                            className="font-bold text-gray-900 dark:text-white truncate uppercase tracking-wide text-sm sm:text-base"
                          >
                            {website.host}
                          </Typography>
                          <Typography
                            variant="body2"
                            className="text-gray-600 dark:text-gray-400 truncate mt-1 text-xs sm:text-sm"
                          >
                            {website.agentName || t('website.noAgent', 'Chưa kết nối Agent')}
                          </Typography>
                        </div>

                        {/* Status icon */}
                        <div className="flex-shrink-0">
                          {website.verify ? (
                            <Icon
                              name="zap"
                              size="sm"
                              className="text-yellow-400 dark:text-yellow-300"
                            />
                          ) : (
                            <Icon
                              name="clock"
                              size="sm"
                              className="text-gray-400 dark:text-gray-500"
                            />
                          )}
                        </div>
                      </div>

                      {/* Action buttons - luôn hiển thị */}
                      <div className="flex items-center justify-end gap-2 pt-2 border-t border-gray-100 dark:border-gray-700">
                        {/* Logo change button */}
                        <div className="relative">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={e => {
                              const file = e.target.files?.[0];
                              if (file) {
                                handleLogoChange(website.id, file);
                              }
                              // Reset input để có thể chọn lại cùng file
                              e.target.value = '';
                            }}
                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                            id={`logo-change-${website.id}`}
                          />
                          <IconCard
                            icon="image"
                            size="sm"
                            variant="primary"
                            title={t('website.changeLogo', 'Đổi logo')}
                            className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10 relative z-0"
                          />
                        </div>

                        {/* Delete button */}
                        <IconCard
                          icon="trash"
                          size="sm"
                          variant="danger"
                          onClick={() => handleDeleteWebsite(website.id)}
                          title={t('common.delete', 'Xóa website')}
                          className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                        />
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        title={t('integration.website.confirmDelete', 'Xác nhận xóa')}
        footer={
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={handleCancelDelete}
              disabled={deleteWebsiteMutation.isPending}
            >
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button
              variant="danger"
              onClick={handleConfirmDelete}
              isLoading={deleteWebsiteMutation.isPending}
              disabled={deleteWebsiteMutation.isPending}
            >
              {deleteWebsiteMutation.isPending
                ? t('integration.website.deleting', 'Đang xóa...')
                : t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="p-4">
          <Typography>
            {t(
              'integration.website.confirmDeleteDesc',
              'Bạn có chắc chắn muốn xóa website này? Hành động này không thể hoàn tác.'
            )}
          </Typography>
        </div>
      </Modal>
    </div>
  );
};

export default WebsiteManagementComponent;
