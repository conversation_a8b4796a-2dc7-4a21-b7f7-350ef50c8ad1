import { lazy, Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading } from '@/shared/components/common';
import i18n from '@/lib/i18n';
import UserIntegrationManagementPage from '../pages/UserIntegrationManagementPage';
import { GHTKProviderForm, GHNProviderForm } from '../shipping/components';

const UserIntegrationTablePage = lazy(() => import('../pages/UserIntegrationTablePage'));

const FacebookIntegrationPage = lazy(() => import('../pages/FacebookIntegrationPage'));
const WebsiteIntegrationPage = lazy(() => import('../pages/WebsiteIntegrationPage'));
const EmailServerManagementPage = lazy(() => import('../pages/EmailServerManagementPage'));
const ProviderModelManagementPage = lazy(() => import('../pages/ProviderModelManagementPage'));
const SMSIntegrationPage = lazy(() => import('../pages/SMSIntegrationPage'));
const DatabaseIntegrationPage = lazy(() => import('../pages/DatabaseIntegrationPage'));
const ZaloOAuthCallbackPage = lazy(() => import('../pages/ZaloOAuthCallbackPage'));

// New integrations
const GoogleCalendarIntegrationPage = lazy(
  () => import('../calendar/pages/GoogleCalendarIntegrationPage')
);
const ShippingIntegrationPage = lazy(() => import('../pages/ShippingIntegrationPage'));
const CloudStorageIntegrationPage = lazy(() => import('../pages/CloudStorageIntegrationPage'));
const EnterpriseStorageIntegrationPage = lazy(
  () => import('../pages/EnterpriseStorageIntegrationPage')
);
const ExternalAgentIntegrationPage = lazy(() => import('../pages/ExternalAgentIntegrationPage'));

// Email and SMS Provider Pages
const EmailProviderPage = lazy(() => import('../pages/EmailProviderPage'));
// const SMTPConfigurationPage = lazy(() => import('../pages/SMTPConfigurationPage'));
// const SMSProviderPage = lazy(() => import('../pages/SMSProviderPage'));
const FptSmsIntegrationPage = lazy(() => import('../sms/pages/FptSmsIntegrationPage'));
const SMSProviderPage = lazy(() => import('../pages/TwilioSMSProviderPage'));

// Banking Integration Pages
const ACBBankingPage = lazy(() => import('../../integrations/pages/ACBBankingPage'));
const MBBankingPage = lazy(() => import('../../integrations/pages/MBBankingPage'));
const OCBBankingPage = lazy(() => import('../../integrations/pages/OCBBankingPage'));
const KienLongBankingPage = lazy(() => import('../../integrations/pages/KienLongBankingPage'));

const t = (key: string, defaultValue?: string) => i18n.t(key, { defaultValue });

const integrationRoutes: RouteObject[] = [
  // Trang chính - Hiển thị tất cả các mạng xã hội
  {
    path: '/integrations/my-integrations',
    element: (
      <MainLayout title={t('integration.social.title')}>
        <Suspense fallback={<Loading />}>
          <UserIntegrationManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang tích hợp của tôi
  {
    path: '/integrations',
    element: (
      <MainLayout title={t('integration.myIntegrations.title', 'Tích hợp của tôi')}>
        <Suspense fallback={<Loading />}>
          <UserIntegrationTablePage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý tài khoản đã liên kết
  {
    path: '/integrations/email/smtp',
    element: (
      <MainLayout title={t('integration.accounts.title')}>
        <Suspense fallback={<Loading />}>
          <EmailServerManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Trang quản lý tài khoản đã liên kết
  {
    path: '/integrations/facebook',
    element: (
      <MainLayout title={t('integration.facebook.title')}>
        <Suspense fallback={<Loading />}>
          <FacebookIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/website',
    element: (
      <MainLayout title={t('integration.website.title')}>
        <Suspense fallback={<Loading />}>
          <WebsiteIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Trang quản lý tích hợp SMS
  {
    path: '/integrations/smtp',
    element: (
      <MainLayout title={t('integration.sms.title')}>
        <Suspense fallback={<Loading />}>
          <SMSIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Trang quản lý tích hợp Database
  {
    path: '/integrations/database',
    element: (
      <MainLayout title={t('integration.database.title')}>
        <Suspense fallback={<Loading />}>
          <DatabaseIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/provider-model',
    element: (
      <MainLayout title={t('integration.providerModel.title')}>
        <Suspense fallback={<Loading />}>
          <ProviderModelManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // ACB Banking Integration
  {
    path: '/integrations/banking/acb',
    element: (
      <MainLayout title={t('integration.banking.acb.title', 'Liên kết tài khoản ACB')}>
        <Suspense fallback={<Loading />}>
          <ACBBankingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // MB Banking Integration
  {
    path: '/integrations/banking/mb',
    element: (
      <MainLayout title={t('integration.banking.mb.title', 'Liên kết tài khoản MB Bank')}>
        <Suspense fallback={<Loading />}>
          <MBBankingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // OCB Banking Integration
  {
    path: '/integrations/banking/ocb',
    element: (
      <MainLayout title={t('integration.banking.ocb.title', 'Liên kết tài khoản OCB')}>
        <Suspense fallback={<Loading />}>
          <OCBBankingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Kiên Long Banking Integration
  {
    path: '/integrations/banking/kienlong',
    element: (
      <MainLayout
        title={t('integration.banking.kienlong.title', 'Liên kết tài khoản Kiên Long Bank')}
      >
        <Suspense fallback={<Loading />}>
          <KienLongBankingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Google Calendar Integration
  {
    path: '/integrations/google-calendar',
    element: (
      <MainLayout title={t('integration.calendar.title')}>
        <Suspense fallback={<Loading />}>
          <GoogleCalendarIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Shipping Integration
  {
    path: '/integrations/shipping',
    element: (
      <MainLayout title={t('integration.shipping.title')}>
        <Suspense fallback={<Loading />}>
          <ShippingIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ghtk',
    element: (
      <MainLayout title={t('integration.shipping.ghtk.title')}>
        <Suspense fallback={<Loading />}>
          <GHTKProviderForm />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/ghn',
    element: (
      <MainLayout title={t('integration.shipping.ghn.title')}>
        <Suspense fallback={<Loading />}>
          <GHNProviderForm />
        </Suspense>
      </MainLayout>
    ),
  },

  // Cloud Storage Integration
  {
    path: '/integrations/cloud-storage',
    element: (
      <MainLayout title={t('integration.cloudStorage.title')}>
        <Suspense fallback={<Loading />}>
          <CloudStorageIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Enterprise Storage Integration
  {
    path: '/integrations/enterprise-storage',
    element: (
      <MainLayout title={t('integration.enterpriseStorage.title')}>
        <Suspense fallback={<Loading />}>
          <EnterpriseStorageIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // External Agent Integration
  {
    path: '/integrations/external-agents',
    element: (
      <MainLayout title={t('integration.externalAgents.title')}>
        <Suspense fallback={<Loading />}>
          <ExternalAgentIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },

  {
    path: '/integrations/email/outlook',
    element: (
      <MainLayout title="Microsoft Outlook">
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/yahoo',
    element: (
      <MainLayout title="Yahoo Mail">
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/sendgrid',
    element: (
      <MainLayout title="SendGrid">
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/mailchimp',
    element: (
      <MainLayout title="Mailchimp Transactional">
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/amazon-ses',
    element: (
      <MainLayout title="Amazon SES">
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/mailgun',
    element: (
      <MainLayout title="Mailgun">
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/email/gmail',
    element: (
      <MainLayout title="Gmail">
        <Suspense fallback={<Loading />}>
          <EmailProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // SMS Provider Routes
  {
    path: '/integrations/sms/twilio',
    element: (
      <MainLayout title="Twilio">
        <Suspense fallback={<Loading />}>
          <SMSProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/sms/ftp',
    element: (
      <MainLayout title="FTP SMS Brandname">
        <Suspense fallback={<Loading />}>
          <FptSmsIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/integrations/sms/vnpt',
    element: (
      <MainLayout title="VNPT SMS">
        <Suspense fallback={<Loading />}>
          <SMSProviderPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Zalo OAuth Callback
  {
    path: '/integration/zalo/oa',
    element: (
      <Suspense fallback={<Loading />}>
        <ZaloOAuthCallbackPage />
      </Suspense>
    ),
  },
];

export default integrationRoutes;
