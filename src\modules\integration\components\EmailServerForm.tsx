import React, { useState, useRef, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Card,
  Typography,
  Button,
  Input,
  Textarea,
  FormItem,
  Form,
  Icon,
  Alert,
} from '@/shared/components/common';
import Toggle from '@/shared/components/common/Toggle';
import { emailServerConfigurationSchema } from '../email/schemas';
import {
  EmailServerConfiguration,
  TestEmailServerWithConfigDto,
  EmailServerConfigDto,
} from '../email/types';
import { useTestEmailServer, useTestEmailServerWithConfig } from '../email/hooks';

// Định nghĩa kiểu dữ liệu cho form
export type EmailServerFormValues = z.infer<typeof emailServerConfigurationSchema>;

interface EmailServerFormProps {
  /**
   * Dữ liệu ban đầu cho form (khi chỉnh sửa)
   */
  initialData?: EmailServerConfiguration | null;

  /**
   * Hàm xử lý khi submit form
   */
  onSubmit: (values: Record<string, unknown>) => void;

  /**
   * Hàm xử lý khi hủy form
   */
  onCancel: () => void;

  /**
   * Trạng thái đang submit form
   */
  isSubmitting?: boolean;

  /**
   * Chế độ chỉ đọc
   */
  readOnly?: boolean;
}

/**
 * Form tạo/chỉnh sửa Email Server Configuration
 */
const EmailServerForm: React.FC<EmailServerFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  readOnly = false,
}) => {
  const { t } = useTranslation(['user', 'common', 'integration']);
  const formRef = useRef(null);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  const testEmailMutation = useTestEmailServer();
  const testEmailWithConfigMutation = useTestEmailServerWithConfig();
  const isEditMode = !!initialData;

  // Test connection states
  const [testEmail, setTestEmail] = useState('');

  // Validation email helper
  const isValidEmail = useCallback((email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, []);

  // Memoize defaultValues để tránh re-render không cần thiết
  const defaultValues = useMemo(() => {
    console.log('🔄 Calculating defaultValues for EmailServerForm', { initialData });
    return initialData
      ? {
          serverName: initialData.serverName,
          host: initialData.host,
          port: initialData.port,
          username: initialData.username,
          password: '', // Không hiển thị password cũ
          useSsl: initialData.useSsl,
          useStartTls: initialData.useStartTls,
          additionalSettings: initialData.additionalSettings
            ? JSON.stringify(initialData.additionalSettings, null, 2)
            : '',
          isActive: initialData.isActive,
        }
      : {
          serverName: '',
          host: '',
          port: 587,
          username: '',
          password: '',
          useSsl: false,
          useStartTls: true,
          additionalSettings: '',
          isActive: true,
        };
  }, [initialData]);

  // Memoize handlers để tránh re-render
  const handleFormSubmit = useCallback(
    (values: Record<string, unknown>) => {
      console.log('📝 Form submit called', { values });
      // Parse additionalSettings từ JSON string nếu có
      const processedValues = {
        ...values,
        additionalSettings: values['additionalSettings']
          ? JSON.parse(values['additionalSettings'] as string)
          : undefined,
      };
      onSubmit(processedValues);
    },
    [onSubmit]
  );

  // Xử lý test kết nối trực tiếp
  const handleTestConnection = useCallback(async () => {
    console.log('🔗 Test connection clicked');

    if (!testEmail) {
      alert(t('integration:email.testEmailRequired', 'Vui lòng nhập email để test'));
      return;
    }

    // Validate email format
    if (!isValidEmail(testEmail)) {
      alert(t('integration:email.invalidEmailFormat', 'Định dạng email không hợp lệ'));
      return;
    }

    // Lấy dữ liệu form hiện tại
    const formData = formRef.current
      ? (formRef.current as { getValues: () => EmailServerFormValues }).getValues()
      : ({} as EmailServerFormValues);
    console.log('📋 Current form data:', formData);

    // Kiểm tra các trường bắt buộc
    if (!formData.host || !formData.username || !formData.password) {
      alert(
        t(
          'integration:email.fillRequiredFields',
          'Vui lòng điền đầy đủ thông tin Host, Username và Password'
        )
      );
      return;
    }

    try {
      const testData = {
        recipientEmail: testEmail,
        subject: 'Test Email từ RedAI - Kiểm tra cấu hình',
      };

      if (isEditMode && initialData?.id) {
        // Nếu đang chỉnh sửa, test với server hiện tại
        const testResponse = await testEmailMutation.mutateAsync({
          id: initialData.id,
          data: testData,
        });

        setTestResult({
          success: testResponse.result.success,
          message: testResponse.result.message || 'Test email đã được gửi thành công!',
        });
      } else {
        // Nếu đang tạo mới, sử dụng API test-with-config
        let additionalSettings = {};
        try {
          if (formData.additionalSettings && formData.additionalSettings.trim()) {
            additionalSettings = JSON.parse(formData.additionalSettings);
          }
        } catch (error) {
          console.warn('Invalid JSON in additionalSettings, using empty object:', error);
        }

        const emailServerConfig: EmailServerConfigDto = {
          serverName: formData.serverName || 'Test Configuration',
          host: formData.host,
          port: formData.port || 587,
          username: formData.username,
          password: formData.password,
          useSsl: formData.useSsl !== undefined ? formData.useSsl : false,
          useStartTls: formData.useStartTls !== undefined ? formData.useStartTls : true,
          additionalSettings,
        };

        const testEmailServerWithConfigData: TestEmailServerWithConfigDto = {
          emailServerConfig,
          testInfo: testData,
        };

        console.log('📤 Sending test request:', testEmailServerWithConfigData);
        const testResponse = await testEmailWithConfigMutation.mutateAsync(
          testEmailServerWithConfigData
        );

        setTestResult({
          success: testResponse.result.success,
          message: testResponse.result.message || 'Test email đã được gửi thành công!',
        });
      }
    } catch (error: unknown) {
      console.error('Error testing connection:', error);
      setTestResult({
        success: false,
        message:
          (error as { response?: { data?: { message?: string } } })?.response?.data?.message ||
          t('integration:email.testError', 'Lỗi khi kiểm tra kết nối'),
      });
    }
  }, [
    testEmail,
    t,
    isEditMode,
    initialData,
    testEmailMutation,
    testEmailWithConfigMutation,
    isValidEmail,
  ]);

  return (
    <Card
      title={
        readOnly
          ? t('admin:integration.email.form.view', 'Xem Email Server')
          : isEditMode
            ? t('admin:integration.email.form.edit', 'Chỉnh sửa Email Server')
            : t('admin:integration.email.form.create', 'Tạo Email Server')
      }
    >
      {/* Form Section */}
      <Form
        schema={emailServerConfigurationSchema}
        onSubmit={handleFormSubmit}
        className="space-y-6"
        defaultValues={defaultValues}
        ref={formRef}
        useDefaultValuesOnce={true}
      >
        <FormItem
          name="serverName"
          label={t('admin:integration.email.form.fields.serverName')}
          required
        >
          <Input
            placeholder={t('admin:integration.email.form.placeholders.serverName')}
            disabled={readOnly || isSubmitting}
            fullWidth
          />
        </FormItem>

        <FormItem name="host" label={t('admin:integration.email.form.fields.host')} required>
          <Input
            placeholder={t('admin:integration.email.form.placeholders.host')}
            disabled={readOnly || isSubmitting}
            fullWidth
          />
        </FormItem>

        <FormItem name="port" label={t('admin:integration.email.form.fields.port')} required>
          <Input
            type="number"
            placeholder={t('admin:integration.email.form.placeholders.port')}
            disabled={readOnly || isSubmitting}
            fullWidth
          />
        </FormItem>

        {/* Manual configuration fields */}
        <FormItem
          name="username"
          label={t('admin:integration.email.form.fields.username')}
          required
        >
          <Input
            type="email"
            placeholder={t('admin:integration.email.form.placeholders.username')}
            disabled={readOnly || isSubmitting}
            fullWidth
          />
        </FormItem>

        <FormItem
          name="password"
          label={t('admin:integration.email.form.fields.password')}
          required={!isEditMode}
        >
          <Input
            type="password"
            placeholder={t('admin:integration.email.form.placeholders.password')}
            disabled={readOnly || isSubmitting}
            fullWidth
          />
        </FormItem>

        {/* SSL/TLS Settings */}
        <div className="flex space-x-8">
          <FormItem name="useSsl" label={t('admin:integration.email.form.fields.useSsl')}>
            <Toggle disabled={readOnly || isSubmitting} label="" />
          </FormItem>

          <FormItem name="useStartTls" label={t('admin:integration.email.form.fields.useStartTls')}>
            <Toggle disabled={readOnly || isSubmitting} label="" />
          </FormItem>

          <FormItem name="isActive" label={t('admin:integration.email.form.fields.isActive')}>
            <Toggle disabled={readOnly || isSubmitting} label="" />
          </FormItem>
        </div>

        {/* Additional Settings */}
        <FormItem
          name="additionalSettings"
          label={t('admin:integration.email.form.fields.additionalSettings')}
        >
          <Textarea
            placeholder={t('admin:integration.email.form.placeholders.additionalSettings')}
            disabled={readOnly || isSubmitting}
            rows={4}
            fullWidth
          />
        </FormItem>

        {/* Test Connection Section */}
        {!readOnly && (
          <div>
            <Typography variant="h6" className="mb-4">
              {t('admin:integration.email.form.test', 'Kiểm tra kết nối')}
            </Typography>

            <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-4">
              {t(
                'admin:integration.email.form.testEmailDescription',
                'Nhập email để nhận email test từ cấu hình này'
              )}
            </Typography>

            <div className="flex space-x-4">
              <div className="flex-1">
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={testEmail}
                  onChange={e => setTestEmail(e.target.value)}
                  leftIcon={<Icon name="mail" size="sm" />}
                  fullWidth
                  error={
                    testEmail && !isValidEmail(testEmail)
                      ? t('integration:email.invalidEmailFormat', 'Định dạng email không hợp lệ')
                      : undefined
                  }
                />
              </div>
              <Button
                type="button"
                variant="primary"
                leftIcon={<Icon name="send" size="sm" />}
                onClick={handleTestConnection}
                disabled={
                  !testEmail ||
                  !isValidEmail(testEmail) ||
                  testEmailMutation.isPending ||
                  testEmailWithConfigMutation.isPending
                }
                isLoading={testEmailMutation.isPending || testEmailWithConfigMutation.isPending}
              >
                {t('admin:integration.email.actions.sendTest', 'Gửi test')}
              </Button>
            </div>

            {/* Test Result */}
            {testResult && (
              <div className="mt-4">
                <Alert
                  type={testResult.success ? 'success' : 'error'}
                  title={testResult.success ? t('common:success') : t('common:error')}
                  message={testResult.message}
                  showIcon
                />
              </div>
            )}
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('admin:integration.email.actions.cancel')}
          </Button>
          {!readOnly && (
            <Button type="submit" variant="primary" isLoading={isSubmitting}>
              {t('admin:integration.email.actions.save')}
            </Button>
          )}
        </div>
      </Form>
    </Card>
  );
};

export default EmailServerForm;
