import React from 'react';
import { IconCard } from '@/shared/components/common';

/**
 * Component test để debug tooltip
 */
const TestTooltip: React.FC = () => {
  return (
    <div className="p-8 space-y-4">
      <h1 className="text-2xl font-bold">Test Tooltip</h1>
      
      {/* Test IconCard với title */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">IconCard với title prop:</h2>
        
        <div className="flex gap-4">
          <IconCard 
            icon="search" 
            title="Tìm kiếm - Test tooltip"
            onClick={() => console.log('Search clicked')}
          />
          
          <IconCard 
            icon="plus" 
            title="Thêm mới - Test tooltip"
            variant="primary"
            onClick={() => console.log('Add clicked')}
          />
          
          <IconCard 
            icon="trash" 
            title="Xóa - Test tooltip"
            variant="danger"
            onClick={() => console.log('Delete clicked')}
          />
        </div>
      </div>
      
      {/* Test debug info */}
      <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded">
        <h3 className="font-semibold mb-2">Debug Info:</h3>
        <ul className="text-sm space-y-1">
          <li>• Hover vào các icon để test tooltip</li>
          <li>• Mở DevTools để xem DOM structure</li>
          <li>• Kiểm tra z-index và CSS classes</li>
        </ul>
      </div>
    </div>
  );
};

export default TestTooltip;
