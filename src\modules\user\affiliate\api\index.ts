import { apiClient } from '@/shared/api';
import {
  AffiliateOverviewData,
  AffiliateStatisticsOverview,
  AffiliatePaginatedResponse,
  AffiliateOrder,
  AffiliateWithdrawal,
  AffiliateCustomer,
  AffiliateContract,
  AffiliatePointConversion,
} from '../types';

/**
 * API calls cho affiliate overview
 */

// Lấy dữ liệu tổng quan affiliate
export const getAffiliateOverview = async (): Promise<AffiliateOverviewData> => {
  const response = await apiClient.get('/user/affiliate/overview');
  return response.result as AffiliateOverviewData;
};

// L<PERSON>y thống kê tổng quan affiliate
export const getAffiliateStatisticsOverview = async (): Promise<AffiliateStatisticsOverview> => {
  const response = await apiClient.get('/user/affiliate/statistics/overview');
  return response.result as AffiliateStatisticsOverview;
};

// Yêu cầu rút tiền
export const requestWithdrawal = async (amount: number) => {
  const response = await apiClient.post('/user/affiliate/withdrawal', {
    amount,
  });
  return response.result;
};

// Đổi point
export const convertPoints = async (points: number) => {
  const response = await apiClient.post('/user/affiliate/convert-points', {
    points,
  });
  return response.result;
};

// Lấy danh sách đơn hàng affiliate
export const getAffiliateOrders = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
}): Promise<AffiliatePaginatedResponse<AffiliateOrder>> => {
  const response = await apiClient.get('/user/affiliate/orders', { params });
  return response.result as AffiliatePaginatedResponse<AffiliateOrder>;
};

// Lấy lịch sử rút tiền
export const getWithdrawalHistory = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
}): Promise<AffiliatePaginatedResponse<AffiliateWithdrawal>> => {
  const response = await apiClient.get('/user/affiliate/withdrawals', { params });
  return response.result as AffiliatePaginatedResponse<AffiliateWithdrawal>;
};

// Lấy danh sách khách hàng
export const getAffiliateCustomers = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
}): Promise<AffiliatePaginatedResponse<AffiliateCustomer>> => {
  const response = await apiClient.get('/user/affiliate/customers', { params });
  return response.result as AffiliatePaginatedResponse<AffiliateCustomer>;
};

// Lấy danh sách hợp đồng affiliate
export const getAffiliateContracts = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
}): Promise<AffiliatePaginatedResponse<AffiliateContract>> => {
  const response = await apiClient.get('/user/affiliate/contracts', { params });
  return response.result as AffiliatePaginatedResponse<AffiliateContract>;
};

// Lấy danh sách lịch sử chuyển đổi điểm
export const getPointConversions = async (params?: {
  page?: number;
  limit?: number;
  status?: string;
}): Promise<AffiliatePaginatedResponse<AffiliatePointConversion>> => {
  const response = await apiClient.get('/user/affiliate/point-conversions', { params });
  return response.result as AffiliatePaginatedResponse<AffiliatePointConversion>;
};
