import React, { useState, useEffect, useRef } from 'react';
import { IconCard, SearchBar, PortalMenu } from '@/shared/components/common';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu/ModernMenu';
import { useTranslation } from 'react-i18next';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';
import ColumnVisibilityMenu from './ColumnVisibilityMenu';

/**
 * Interface định nghĩa cấu trúc hiển thị cột
 */
export interface ColumnVisibility {
  id: string;
  label: string;
  visible: boolean;
}

/**
 * Interface định nghĩa map nhãn cho các cột
 * Key là id của cột, value là key translation hoặc nhãn trực tiếp
 */
export interface ColumnLabelMap {
  [columnId: string]: string;
}

/**
 * Interface định nghĩa cấu trúc cột từ Table component
 * Đ<PERSON><PERSON><PERSON> sử dụng để lấy thông tin title từ columns
 */
export interface TableColumnInfo {
  key: string;
  title: string | React.ReactNode;
  dataIndex?: string;
  width?: string | number;
  sortable?: boolean;
}

/**
 * Định nghĩa cấu trúc cho icon bổ sung
 */
export interface AdditionalIcon {
  /**
   * Icon cần hiển thị
   */
  icon: string;

  /**
   * Tooltip cho icon
   */
  tooltip: string;

  /**
   * Variant của icon
   */
  variant?: 'default' | 'primary' | 'secondary' | 'ghost';

  /**
   * Hàm xử lý khi click vào icon
   */
  onClick: () => void;

  /**
   * Class bổ sung cho icon
   */
  className?: string;

  /**
   * Điều kiện hiển thị icon
   */
  condition?: boolean;
}

interface MenuIconBarProps {
  onSearch: (term: string) => void;
  onAdd?: () => void;
  onDateRangeChange?: (range: [Date | null, Date | null]) => void;
  onColumnVisibilityChange?: (columns: ColumnVisibility[]) => void;
  columns?: ColumnVisibility[];
  columnLabelMap?: ColumnLabelMap;
  tableColumns?: TableColumnInfo[];
  showDateFilter?: boolean;
  showColumnFilter?: boolean;
  /**
   * Danh sách các menu items để hiển thị trong filter menu
   */
  items?: ModernMenuItem[];
  /**
   * Các icon bổ sung hiển thị bên cạnh các icon mặc định
   */
  additionalIcons?: AdditionalIcon[];
  /**
   * Trạng thái loading để hiển thị loading indicator
   */
  isLoading?: boolean;
}

/**
 * Component hiển thị thanh icon chức năng với các tính năng nâng cao:
 * - Tìm kiếm
 * - Thêm mới
 * - Tùy chỉnh hiển thị cột
 */
const MenuIconBar: React.FC<MenuIconBarProps> = ({
  onSearch,
  onAdd,
  onColumnVisibilityChange,
  columns = [],
  columnLabelMap = {},
  tableColumns = [],
  // showDateFilter = true,
  showColumnFilter = true,
  items = [],
  additionalIcons = [],
  isLoading = false,
}) => {
  const { t } = useTranslation();
  const [showSearch, setShowSearch] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const [showColumnVisibility, setShowColumnVisibility] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const [columnSettings, setColumnSettings] = useState<ColumnVisibility[]>(columns || []);

  // Refs
  const triggerRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLDivElement>(null);

  // Cập nhật columnSettings khi columns prop thay đổi
  useEffect(() => {
    // Đảm bảo columns không null/undefined
    const safeColumns = columns || [];

    // Chỉ update nếu có thay đổi thực sự về cấu trúc hoặc nội dung
    setColumnSettings(prevSettings => {
      // Nếu không có columns hoặc columns rỗng, giữ nguyên state hiện tại
      if (safeColumns.length === 0) {
        return prevSettings;
      }

      // So sánh độ dài trước
      if (safeColumns.length !== prevSettings.length) {
        return safeColumns;
      }

      // So sánh từng phần tử
      const hasChanges = safeColumns.some((col, index) => {
        const prevCol = prevSettings[index];
        return (
          !prevCol ||
          col.id !== prevCol.id ||
          col.label !== prevCol.label ||
          col.visible !== prevCol.visible
        );
      });

      return hasChanges ? safeColumns : prevSettings;
    });
  }, [columns]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearch(false);
      }
    };

    if (showSearch) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSearch]);

  // Đóng tất cả các menu khác khi mở một menu mới
  const closeAllMenus = () => {
    setShowSearch(false);
    setShowColumnVisibility(false);
  };

  const handleSearchClick = () => {
    if (showSearch) {
      setShowSearch(false);
      // Nếu đang có từ khóa tìm kiếm, reset và gọi lại API
      if (searchTerm) {
        setSearchTerm('');
        onSearch('');
      }
    } else {
      closeAllMenus();
      setShowSearch(true);
    }
  };

  // Xử lý khi giá trị tìm kiếm thay đổi (chỉ cập nhật state, không gọi onSearch)
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  // Xử lý khi người dùng nhấn nút tìm kiếm hoặc submit form
  const handleSearchSubmit = (value: string) => {
    onSearch(value);
  };

  const handleAddClick = () => {
    if (onAdd) {
      onAdd();
    }
  };

  const handleColumnVisibilityChange = (columnId: string, checked: boolean) => {
    // Kiểm tra an toàn cho columnSettings
    if (!columnSettings || !Array.isArray(columnSettings)) {
      console.warn('columnSettings is not available or not an array');
      return;
    }

    // Xử lý trường hợp "Chọn tất cả"
    if (columnId === 'all') {
      // Nếu chọn "Chọn tất cả", hiện tất cả các cột
      if (checked) {
        const updatedColumns = columnSettings.map(col => ({
          ...col,
          visible: true,
        }));
        setColumnSettings(updatedColumns);
        if (onColumnVisibilityChange) {
          onColumnVisibilityChange(updatedColumns);
        }
      }
      // Nếu bỏ chọn "Chọn tất cả", ẩn tất cả các cột
      else {
        const updatedColumns = columnSettings.map(col => ({
          ...col,
          visible: false,
        }));
        setColumnSettings(updatedColumns);
        if (onColumnVisibilityChange) {
          onColumnVisibilityChange(updatedColumns);
        }
      }
      return;
    }

    // Xử lý trường hợp chọn/bỏ chọn một cột cụ thể
    const updatedColumns = [...columnSettings];

    // Tìm và cập nhật cột được chọn/bỏ chọn
    const columnToUpdate = updatedColumns.find(col => col.id === columnId);
    if (columnToUpdate) {
      columnToUpdate.visible = checked;
    }

    // Luôn bỏ chọn "Chọn tất cả" khi có bất kỳ cột nào bị bỏ chọn
    if (!checked) {
      const allColumn = updatedColumns.find(col => col.id === 'all');
      if (allColumn) {
        allColumn.visible = false;
      }
    }
    // Chỉ chọn "Chọn tất cả" khi tất cả các cột khác đều được chọn
    else {
      const otherColumns = updatedColumns.filter(col => col.id !== 'all');
      const allSelected = otherColumns.every(col => col.visible);
      const allColumn = updatedColumns.find(col => col.id === 'all');
      if (allColumn) {
        allColumn.visible = allSelected;
      }
    }

    setColumnSettings(updatedColumns);
    if (onColumnVisibilityChange) {
      onColumnVisibilityChange(updatedColumns);
    }
  };

  const toggleColumnVisibility = () => {
    if (showColumnVisibility) {
      setShowColumnVisibility(false);
    } else {
      closeAllMenus();

      // Tính toán vị trí menu
      if (triggerRef.current) {
        const rect = triggerRef.current.getBoundingClientRect();
        const menuWidth = 220;
        const menuHeight = 300; // Ước tính chiều cao menu
        const margin = 8;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Tính toán vị trí left, căn giữa menu với trigger icon
        const triggerCenter = rect.left + rect.width / 2;
        let left = triggerCenter - menuWidth / 2; // Căn giữa menu với trigger

        // Điều chỉnh vị trí để tránh tràn màn hình
        if (left < margin) {
          left = margin;
        }
        if (left + menuWidth > viewportWidth - margin) {
          left = viewportWidth - menuWidth - margin;
        }

        // Tính toán vị trí top
        let top = rect.bottom + margin;
        if (top + menuHeight > viewportHeight - margin) {
          top = rect.top - menuHeight - margin;
        }
        top = Math.max(margin, top);

        setMenuPosition({ top, left });
        setShowColumnVisibility(true);
      }
    }
  };

  // Hàm lấy nhãn cho cột dựa trên tableColumns, columnLabelMap hoặc label mặc định
  const getColumnLabel = (column: ColumnVisibility): string => {
    // Ưu tiên 1: Nếu có trong columnLabelMap, sử dụng translation
    const labelKey = columnLabelMap[column.id];
    if (labelKey) {
      return t(labelKey, labelKey);
    }

    // Ưu tiên 2: Tìm trong tableColumns
    if (tableColumns && tableColumns.length > 0) {
      const tableColumn = tableColumns.find(col => col.key === column.id);
      if (tableColumn && typeof tableColumn.title === 'string') {
        // Nếu title là string và có dạng 'namespace:key', sử dụng translation
        if (tableColumn.title.includes(':')) {
          return t(tableColumn.title, tableColumn.title);
        }
        // Nếu title là string thông thường, trả về trực tiếp
        return tableColumn.title;
      }
    }

    // Ưu tiên 3: Sử dụng label mặc định từ column
    return column.label;
  };

  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-3 mb-4">
      <div className="flex items-center space-x-2">
        {/* Add Button */}
        {onAdd && (
          <IconCard
            icon="plus"
            variant="primary"
            onClick={handleAddClick}
            title={t('common.addNew')}
          />
        )}

        {/* Search Icon */}
        <IconCard
          icon="search"
          variant={showSearch ? 'primary' : 'default'}
          onClick={handleSearchClick}
          active={false}
          className={showSearch ? 'shadow-md' : ''}
          title={t('common.search')}
        />

        {/* Filter Menu Items */}
        {items && items.length > 0 && (
          <ModernMenuTrigger
            items={items
              .filter(item => item.onClick)
              .map(item => ({
                label: item.label,
                icon: item.icon,
                onClick: item.onClick!,
                keepOpen: true, // Keep menu open for filter selections
                ...(item.disabled !== undefined && { disabled: item.disabled }),
                ...(item.divider !== undefined && { divider: item.divider }),
              }))}
            triggerIcon="filter"
            triggerVariant="default"
            placement="bottom"
            width="200px"
          />
        )}

        {/* Column Visibility button */}
        {showColumnFilter && columns.length > 0 && (
          <div className="relative" ref={triggerRef}>
            <IconCard
              icon="layers"
              variant={showColumnVisibility ? 'primary' : 'default'}
              onClick={toggleColumnVisibility}
              active={false}
              className={showColumnVisibility ? 'shadow-md' : ''}
              title={t('common.columnVisibility', 'Hiển thị cột')}
            />

            {/* Portal Menu */}
            {showColumnVisibility && (
              <PortalMenu
                menuRef={menuRef}
                position={menuPosition}
                onClose={() => setShowColumnVisibility(false)}
              >
                <ColumnVisibilityMenu
                  columnSettings={columnSettings}
                  onColumnVisibilityChange={handleColumnVisibilityChange}
                  getColumnLabel={getColumnLabel}
                />
              </PortalMenu>
            )}
          </div>
        )}

        {/* Loading Indicator */}
        {isLoading && (
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
          </div>
        )}

        {/* Additional Icons */}
        {additionalIcons.map(
          (iconConfig, index) =>
            // Chỉ hiển thị icon nếu không có điều kiện hoặc điều kiện là true
            (iconConfig.condition === undefined || iconConfig.condition) && (
              <IconCard
                key={index}
                icon={iconConfig.icon}
                variant={iconConfig.variant || 'default'}
                onClick={iconConfig.onClick}
                active={false}
                className={iconConfig.className || ''}
                title={iconConfig.tooltip}
              />
            )
        )}
      </div>

      {/* Search Bar */}
      <div className="w-full sm:w-auto" ref={searchRef}>
        <SearchBar
          visible={showSearch}
          value={searchTerm}
          onChange={handleSearchChange}
          onSubmit={handleSearchSubmit}
          onToggle={handleSearchClick}
          maxWidth="100%"
          variant="flat"
          autoFocus={true}
          showSearchIcon={false}
          className="w-full"
          searchOnEnter={true}
        />
      </div>
    </div>
  );
};

export default MenuIconBar;
